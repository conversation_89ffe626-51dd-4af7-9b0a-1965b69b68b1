<!-- Import Students Modal -->
<div id="importModal" tabindex="-1" aria-hidden="true" class="hidden overflow-y-auto overflow-x-hidden fixed top-0 right-0 left-0 z-50 justify-center items-center w-full md:inset-0 h-[calc(100%-1rem)] max-h-full" data-modal-backdrop="static" data-modal-placement="center-center">
    <div class="relative p-4 w-full max-w-2xl max-h-full">
        <!-- Modal content -->
        <div class="relative bg-white rounded-lg shadow dark:bg-gray-700">
            <!-- Modal header -->
            <div class="flex items-center justify-between p-4 md:p-5 border-b rounded-t dark:border-gray-600 bg-green-600">
                <h3 class="text-xl font-semibold text-white">
                    Import Students from Excel
                </h3>
                <button type="button" class="text-white bg-transparent hover:bg-green-700 rounded-lg text-sm w-8 h-8 ms-auto inline-flex justify-center items-center" data-modal-hide="importModal">
                    <i class="fas fa-times"></i>
                    <span class="sr-only">Close modal</span>
                </button>
            </div>
            <!-- Modal body -->
            <form action="{{ route('student-cgpa.import') }}" method="POST" enctype="multipart/form-data">
                @csrf
                <div class="p-4 md:p-5 space-y-4">
                    <!-- Info alert -->
                    <div class="p-4 mb-4 text-blue-800 rounded-lg bg-blue-50 dark:bg-gray-800 dark:text-blue-400" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-info-circle mr-2"></i>
                            <h3 class="text-lg font-medium">Import Instructions</h3>
                        </div>
                        <div class="mt-2 text-sm">
                            <ol class="list-decimal pl-5 space-y-1">
                                <li>Download the Excel template file to ensure correct formatting.</li>
                                <li>Fill in the student data following the template format.</li>
                                <li>Upload the completed Excel file below.</li>
                            </ol>
                        </div>
                    </div>
                    
                    <!-- Download template button -->
                    <div class="flex justify-center mb-4">
                        <a href="{{ route('student-cgpa.template') }}" class="text-blue-700 hover:text-white border border-blue-700 hover:bg-blue-800 focus:ring-4 focus:outline-none focus:ring-blue-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center me-2 mb-2 dark:border-blue-500 dark:text-blue-500 dark:hover:text-white dark:hover:bg-blue-600 dark:focus:ring-blue-800 inline-flex items-center">
                            <i class="fas fa-file-download mr-2"></i> Download Template
                        </a>
                    </div>
                    
                    <!-- File input -->
                    <div class="mb-4">
                        <label for="importFile" class="block mb-2 text-sm font-medium text-gray-900 dark:text-white">
                            Select Excel File <span class="text-red-500">*</span>
                        </label>
                        <input class="block w-full text-sm text-gray-900 border border-gray-300 rounded-lg cursor-pointer bg-gray-50 dark:text-gray-400 focus:outline-none dark:bg-gray-700 dark:border-gray-600 dark:placeholder-gray-400" 
                               id="importFile" 
                               name="file" 
                               type="file" 
                               accept=".xlsx,.xls,.csv" 
                               required>
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-300">
                            Only Excel files (.xlsx, .xls, .csv) are allowed. Max file size: 10MB
                        </p>
                    </div>
                    
                    <!-- Warning alert -->
                    <div class="p-4 mb-4 text-yellow-800 rounded-lg bg-yellow-50 dark:bg-gray-800 dark:text-yellow-300" role="alert">
                        <div class="flex items-center">
                            <i class="fas fa-exclamation-triangle mr-2"></i>
                            <h3 class="text-lg font-medium">Important Notes</h3>
                        </div>
                        <div class="mt-2 text-sm">
                            <ul class="list-disc pl-5 space-y-1">
                                <li>Required fields: Index Number, Name, GPA, Program, Department, Semester, Year of Enrollment</li>
                                <li>Index Numbers must be unique</li>
                                <li>GPA and CGPA must be between 0.00 and 4.00</li>
                                <li>Year of Enrollment must be a valid year</li>
                            </ul>
                        </div>
                    </div>
                </div>
                
                <!-- Modal footer -->
                <div class="flex items-center justify-end p-4 md:p-5 border-t border-gray-200 rounded-b dark:border-gray-600">
                    <button type="submit" class="text-white bg-green-600 hover:bg-green-700 focus:ring-4 focus:outline-none focus:ring-green-300 font-medium rounded-lg text-sm px-5 py-2.5 text-center dark:bg-green-600 dark:hover:bg-green-700 dark:focus:ring-green-800 inline-flex items-center">
                        <i class="fas fa-file-import mr-2"></i> Import Students
                    </button>
                    <button type="button" class="ms-3 text-gray-500 bg-white hover:bg-gray-100 focus:ring-4 focus:outline-none focus:ring-gray-200 rounded-lg border border-gray-200 text-sm font-medium px-5 py-2.5 hover:text-gray-900 focus:z-10 dark:bg-gray-700 dark:text-gray-300 dark:border-gray-500 dark:hover:text-white dark:hover:bg-gray-600 dark:focus:ring-gray-600" data-modal-hide="importModal">
                        Cancel
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
