<?php

namespace App\Exports;

use App\Models\StudentCgpa;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StudentCgpaExport implements FromCollection, WithHeadings, WithMapping, ShouldAutoSize, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        return StudentCgpa::all();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Index Number',
            'Name',
            'GPA',
            'CGPA',
            'Total Credit',
            'Total Point',
            'Program',
            'Department',
            'Semester',
            'Year of Enrollment',
            'Email',
            'Created At',
            'Updated At',
        ];
    }

    /**
     * @param mixed $student
     *
     * @return array
     */
    public function map($student): array
    {
        return [
            $student->index_number,
            $student->name,
            $student->gpa,
            $student->cgpa,
            $student->totalcredit,
            $student->totalpoint,
            $student->program,
            $student->department,
            $student->semester,
            $student->year_of_enrollment,
            $student->email,
            $student->created_at->format('Y-m-d H:i:s'),
            $student->updated_at->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * @param Worksheet $sheet
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold text
            1 => ['font' => ['bold' => true]],
        ];
    }
}
