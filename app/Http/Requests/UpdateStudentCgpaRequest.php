<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdateStudentCgpaRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Will be handled by auth middleware
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $studentId = $this->route('student_cgpa');
        
        return [
            'index_number' => 'required|string|max:50|unique:cgpa,index_number,' . $studentId,
            'name' => 'required|string|max:255',
            'gpa' => 'required|numeric|min:0|max:4.00',
            'cgpa' => 'required|numeric|min:0|max:4.00',
            'totalcredit' => 'required|integer|min:0',
            'totalpoint' => 'required|numeric|min:0',
            'program' => 'required|string|max:100',
            'department' => 'required|string|max:100',
            'semester' => 'required|string|max:20',
            'year_of_enrollment' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            'email' => 'nullable|email|max:100',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array<string, string>
     */
    public function messages(): array
    {
        return [
            'index_number.unique' => 'A student with this index number already exists.',
            'gpa.max' => 'GPA cannot be greater than 4.00.',
            'cgpa.max' => 'CGPA cannot be greater than 4.00.',
            'year_of_enrollment.max' => 'Year of enrollment cannot be in the future.',
        ];
    }
}
