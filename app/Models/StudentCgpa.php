<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class StudentCgpa extends Model
{
    use SoftDeletes;

    protected $fillable = [
        'index_number',
        'name',
        'gpa',
        'cgpa',
        'totalcredit',
        'totalpoint',
        'program',
        'department',
        'semester',
        'year_of_enrollment',
        'email',
    ];

    protected $casts = [
        'gpa' => 'decimal:2',
        'cgpa' => 'decimal:2',
        'totalpoint' => 'decimal:2',
        'year_of_enrollment' => 'integer',
    ];
}
