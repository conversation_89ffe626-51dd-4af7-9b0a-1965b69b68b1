<?php

namespace App\Imports;

use App\Models\StudentCgpa;
use Maatwebsite\Excel\Concerns\ToModel;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnError;
use Maatwebsite\Excel\Concerns\SkipsErrors;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Validators\Failure;
use Illuminate\Validation\Rule;

class StudentCgpaImport implements ToModel, WithHeadingRow, WithValidation, SkipsOnError, SkipsOnFailure
{
    use SkipsErrors, SkipsFailures;

    /**
     * @param array $row
     *
     * @return \Illuminate\Database\Eloquent\Model|null
     */
    public function model(array $row)
    {
        return new StudentCgpa([
            'index_number'      => $row['index_number'],
            'name'              => $row['name'],
            'gpa'               => $row['gpa'],
            'cgpa'              => $row['cgpa'] ?? 0.00,
            'totalcredit'       => $row['totalcredit'] ?? 0,
            'totalpoint'        => $row['totalpoint'] ?? 0.00,
            'program'           => $row['program'],
            'department'        => $row['department'],
            'semester'          => $row['semester'],
            'year_of_enrollment'=> $row['year_of_enrollment'],
            'email'             => $row['email'] ?? null,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            '*.index_number' => [
                'required',
                'string',
                'max:50',
                Rule::unique('cgpa', 'index_number')
            ],
            '*.name' => 'required|string|max:255',
            '*.gpa' => 'required|numeric|min:0|max:4.00',
            '*.cgpa' => 'nullable|numeric|min:0|max:4.00',
            '*.totalcredit' => 'nullable|integer|min:0',
            '*.totalpoint' => 'nullable|numeric|min:0',
            '*.program' => 'required|string|max:100',
            '*.department' => 'required|string|max:100',
            '*.semester' => 'required|string|max:20',
            '*.year_of_enrollment' => 'required|integer|min:1900|max:' . (date('Y') + 1),
            '*.email' => 'nullable|email|max:100',
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'index_number.unique' => 'The index number :input already exists in the system.',
            'gpa.max' => 'GPA cannot be greater than 4.00.',
            'cgpa.max' => 'CGPA cannot be greater than 4.00.',
            'year_of_enrollment.max' => 'Year of enrollment cannot be in the future.',
        ];
    }
}
