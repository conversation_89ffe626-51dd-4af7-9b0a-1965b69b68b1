<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithTitle;
use Maatwebsite\Excel\Concerns\WithColumnFormatting;
use PhpOffice\PhpSpreadsheet\Style\NumberFormat;

class StudentCgpaTemplateExport implements FromArray, WithHeadings, WithTitle, WithColumnFormatting
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Sample data row
        return [
            [
                'INDEX001',
                'John Doe',
                3.5,
                3.4,
                120,
                408.00,
                'Computer Science',
                'Computing and Information Science',
                'Second Semester',
                2023,
                '<EMAIL>'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'index_number',
            'name',
            'gpa',
            'cgpa',
            'totalcredit',
            'totalpoint',
            'program',
            'department',
            'semester',
            'year_of_enrollment',
            'email'
        ];
    }

    /**
     * @return string
     */
    public function title(): string
    {
        return 'CGPA Template';
    }

    /**
     * @return array
     */
    public function columnFormats(): array
    {
        return [
            'C' => '0.00', // GPA
            'D' => '0.00', // CGPA
            'E' => '0',    // Total Credit
            'F' => '0.00', // Total Point
            'J' => '0'     // Year of Enrollment
        ];
    }
}
