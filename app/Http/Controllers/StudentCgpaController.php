<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreStudentCgpaRequest;
use App\Http\Requests\UpdateStudentCgpaRequest;
use App\Models\StudentCgpa;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\StudentCgpaImport;
use App\Exports\StudentCgpaExport;
use App\Exports\StudentCgpaTemplateExport;
use Illuminate\Support\Facades\Storage;
use Flasher\Prime\FlasherInterface;
use Carbon\Carbon;

class StudentCgpaController extends Controller
{
    protected $flasher;

    public function __construct(FlasherInterface $flasher)
    {
        $this->flasher = $flasher;
    }

    /**
     * Display a listing of the resource with search and filters.
     */
    public function index(Request $request)
    {
        $query = StudentCgpa::query();

        // Search by name or index number
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('index_number', 'like', "%{$search}%");
            });
        }

        // Filter by program
        if ($request->has('program') && !empty($request->program)) {
            $query->where('program', $request->program);
        }

        // Filter by department
        if ($request->has('department') && !empty($request->department)) {
            $query->where('department', $request->department);
        }

        // Filter by semester
        if ($request->has('semester') && !empty($request->semester)) {
            $query->where('semester', $request->semester);
        }

        // Get unique values for filters
        $programs = StudentCgpa::distinct()->pluck('program');
        $departments = StudentCgpa::distinct()->pluck('department');
        $semesters = StudentCgpa::distinct()->pluck('semester');

        // Paginate results
        $students = $query->latest()->paginate(15);

        return view('student-cgpa.index', compact('students', 'programs', 'departments', 'semesters'));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
return view('student-cgpa.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreStudentCgpaRequest $request)
    {
        try {
            $student = StudentCgpa::create($request->validated());
            $this->flasher->addSuccess('Student CGPA record created successfully!');
            return response()->json(['success' => true, 'message' => 'Student CGPA record created successfully!']);
        } catch (\Exception $e) {
            $this->flasher->addError('Error creating student CGPA record: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error creating student CGPA record.'], 500);
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(StudentCgpa $studentCgpa)
    {
        return response()->json($studentCgpa);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(StudentCgpa $studentCgpa)
    {
        return response()->json($studentCgpa);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateStudentCgpaRequest $request, StudentCgpa $studentCgpa)
    {
        try {
            $studentCgpa->update($request->validated());
            $this->flasher->addSuccess('Student CGPA record updated successfully!');
            return response()->json(['success' => true, 'message' => 'Student CGPA record updated successfully!']);
        } catch (\Exception $e) {
            $this->flasher->addError('Error updating student CGPA record: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error updating student CGPA record.'], 500);
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(StudentCgpa $studentCgpa)
    {
        try {
            $studentCgpa->delete();
            $this->flasher->addSuccess('Student CGPA record deleted successfully!');
            return response()->json(['success' => true, 'message' => 'Student CGPA record deleted successfully!']);
        } catch (\Exception $e) {
            $this->flasher->addError('Error deleting student CGPA record: ' . $e->getMessage());
            return response()->json(['success' => false, 'message' => 'Error deleting student CGPA record.'], 500);
        }
    }

    /**
     * Show the import form.
     */
    public function showImportForm()
    {
return view('student-cgpa.import');
    }

    /**
     * Import students from Excel file.
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:10240',
        ]);

        try {
            Excel::import(new StudentCgpaImport, $request->file('file'));
            $this->flasher->addSuccess('Students imported successfully!');
            return redirect()->route('admin.student-cgpa.index');
        } catch (\Exception $e) {
            $this->flasher->addError('Error importing students: ' . $e->getMessage());
            return back()->withInput();
        }
    }

    /**
     * Export students to Excel file.
     */
    public function export()
    {
        $date = Carbon::now()->format('Y-m-d');
        return Excel::download(new StudentCgpaExport, "students_cgpa_{$date}.xlsx");
    }

    /**
     * Download the import template.
     *
     * @return \Symfony\Component\HttpFoundation\BinaryFileResponse
     */
    public function downloadTemplate()
    {
        $fileName = 'student_cgpa_import_template.xlsx';
        $filePath = storage_path('app/public/templates/' . $fileName);
        
        // Ensure the directory exists
        if (!file_exists(dirname($filePath))) {
            mkdir(dirname($filePath), 0755, true);
        }
        
        // Generate and save the template
        Excel::store(new StudentCgpaTemplateExport, 'public/templates/' . $fileName);
        
        // Return the file for download
        return response()->download($filePath, $fileName, [
            'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        ])->deleteFileAfterSend(true);
    }
}
