<?php

namespace App\Livewire;

use Livewire\Component;
use Livewire\WithPagination;
use App\Models\IncomingMemo;
use Illuminate\Support\Facades\Auth;

#[\Livewire\Attributes\Layout('layouts.app')]
class IncomingMemoComponent extends Component
{
    use WithPagination;

    // Form fields
    public $date_received, $registry_no, $from_whom_received, $date_of_letter, $letter_no, $subject, $remark;

    // Component state
    public $showModal = false;
    public $editingId = null;
    public $search = '';
    public $filters = [
        'date_from' => '',
        'date_to' => '',
        'sender' => ''
    ];

    protected $rules = [
        'date_received' => 'required|date',
        'registry_no' => 'required|integer|max:20|unique:incoming_memos,registry_no',
        'from_whom_received' => 'required|string|max:100',
        'date_of_letter' => 'required|date|before_or_equal:date_received',
        'letter_no' => 'required|integer|max:20',
        'subject' => 'required|string|min:100',
        'remark' => 'required|string|max:100',
    ];

    // Updated listeners for Livewire v3
    protected $listeners = ['refreshComponent' => '$refresh'];

    public function mount()
    {
        $this->resetForm();
    }

    public function resetForm()
    {
        $this->reset([
            'date_received', 'registry_no', 'from_whom_received',
            'date_of_letter', 'letter_no', 'subject', 'remark',
            'editingId'
        ]);
        $this->resetValidation();
    }

    public function create()
    {
        $this->resetForm();
        $this->showModal = true;
    }

    public function closeModal()
    {
        $this->showModal = false;
        $this->resetForm();
    }

    public function edit($id)
    {
        try {
            $memo = IncomingMemo::findOrFail($id);

            $this->editingId = $id;
            $this->date_received = $memo->date_received->format('Y-m-d');
            $this->registry_no = $memo->registry_no;
            $this->from_whom_received = $memo->from_whom_received;
            $this->date_of_letter = $memo->date_of_letter->format('Y-m-d');
            $this->letter_no = $memo->letter_no;
            $this->subject = $memo->subject;
            $this->remark = $memo->remark;

            $this->showModal = true;
        } catch (\Exception $e) {
            $this->dispatch('toast', message: 'Error loading memo', type: 'error');
        }
    }

    public function save()
    {
        try {
            $rules = $this->rules;

            // Handle unique validation for edit mode
            if ($this->editingId) {
                $rules['registry_no'] = 'required|integer|max:20|unique:incoming_memos,registry_no,' . $this->editingId;
            }

            $validated = $this->validate($rules);

            if (empty($validated['date_received'])) {
                flash()->error('Date received is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['date_received' => ['Date received is required.']],
                    'date_received'
                );
            }

            if (empty($validated['registry_no'])) {
                flash()->error('Registry number is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['registry_no' => ['Registry number is required.']],
                    'registry_no'
                );
            }

            if (empty($validated['from_whom_received'])) {
                flash()->error('Sender is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['from_whom_received' => ['Sender is required.']],
                    'from_whom_received'
                );
            }

            if (empty($validated['date_of_letter'])) {
                flash()->error('Date of letter is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['date_of_letter' => ['Date of letter is required.']],
                    'date_of_letter'
                );
            }

            if (empty($validated['letter_no'])) {
                flash()->error('Letter number is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['letter_no' => ['Letter number is required.']],
                    'letter_no'
                );
            }

            if (empty($validated['subject'])) {
                flash()->error('Subject is required.', ['title' => 'Error']);
                throw new \Illuminate\Validation\ValidationException(
                    \Illuminate\Validation\Validator::make([], []),
                    ['subject' => ['Subject is required.']],
                    'subject'
                );
            }

            $data = [
                'date_received' => $validated['date_received'],
                'registry_no' => $validated['registry_no'],
                'from_whom_received' => $validated['from_whom_received'],
                'date_of_letter' => $validated['date_of_letter'],
                'letter_no' => $validated['letter_no'],
                'subject' => $validated['subject'],
                'remark' => $validated['remark'] ?? null,
                'created_by' => Auth::id(),
            ];

            if ($this->editingId) {
                IncomingMemo::findOrFail($this->editingId)->update($data);
                flash()->success('Memo updated successfully.', ['title' => 'Success']);
            } else {
                IncomingMemo::create($data);
                flash()->success('Memo created successfully.', ['title' => 'Success']);
            }

            $this->closeModal();
            $this->resetPage(); // Reset pagination to show updated results
            
        } catch (\Illuminate\Validation\ValidationException $e) {
            // Let Livewire handle validation errors automatically
            throw $e;
        } catch (\Exception $e) {
            \Log::error('Incoming memo save error: ' . $e->getMessage());
            flash()->error('An error occurred while saving the memo. Please try again.', ['title' => 'Error']);
        }
    }

    public function confirmDelete($id)
    {
        try {
            $memo = IncomingMemo::findOrFail($id);
            $memo->delete();
            flash()->success('Memo deleted successfully.', ['title' => 'Success']);
            $this->resetPage(); // Reset pagination after deletion
        } catch (\Exception $e) {
            \Log::error('Incoming memo delete error: ' . $e->getMessage());
            flash()->error('Error deleting memo. Please try again.', ['title' => 'Error']);
        }
    }

    // Keep the old delete method for backward compatibility
    public function delete($id)
    {
        $this->confirmDelete($id);
    }

    public function resetFilters()
    {
        $this->reset('filters', 'search');
        $this->resetPage(); // Reset pagination when filters are reset
    }

    // Add method to handle search input changes
    public function updatedSearch()
    {
        $this->resetPage();
    }

    // Add methods to handle filter changes
    public function updatedFilters()
    {
        $this->resetPage();
    }

    public function render()
    {
        $memos = IncomingMemo::query()
            ->when($this->search, function ($query) {
                $query->where(function ($q) {
                    $q->where('registry_no', 'like', '%' . $this->search . '%')
                      ->orWhere('from_whom_received', 'like', '%' . $this->search . '%')
                      ->orWhere('letter_no', 'like', '%' . $this->search . '%')
                      ->orWhere('subject', 'like', '%' . $this->search . '%');
                  
                });
            })
            ->when($this->filters['date_from'], function ($query) {
                $query->where('date_received', '>=', $this->filters['date_from']);
            })
            ->when($this->filters['date_to'], function ($query) {
                $query->where('date_received', '<=', $this->filters['date_to']);
            })
            ->when($this->filters['sender'], function ($query) {
                $query->where('from_whom_received', 'like', '%' . $this->filters['sender'] . '%');
            })
            ->orderBy('date_received', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(20);

        return view('livewire.incoming-memo', [
            'memos' => $memos,
            'header' => 'Incoming Memos'
        ]);
    }
}