@extends('layouts.app')

@section('title', 'Student CGPA Management')

@section('main')
<div class="container mx-auto px-4 sm:px-6 lg:px-8 py-6">
    <div class="bg-white dark:bg-gray-800 shadow rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200 dark:border-gray-700 flex justify-between items-center">
            <h2 class="text-lg font-semibold text-gray-900 dark:text-white">Student CGPA Management</h2>
            <div class="flex space-x-2">
                <button type="button" 
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        data-modal-toggle="importModal">
                    <i class="fas fa-file-import mr-2"></i> Import Excel
                </button>
                <a href="{{ route('student-cgpa.export') }}" 
                   class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-green-600 hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500">
                    <i class="fas fa-file-export mr-2"></i> Export Excel
                </a>
                <button type="button" 
                        class="inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        data-modal-toggle="addStudentModal">
                    <i class="fas fa-plus mr-2"></i> Add Student
                </button>
            </div>
        </div>
        
        <div class="p-6">
            <!-- Search and Filters -->
            <form action="{{ route('student-cgpa.index') }}" method="GET" class="mb-6">
                <div class="grid grid-cols-1 md:grid-cols-6 gap-4">
                    <div class="col-span-1 md:col-span-2">
                        <input type="text" 
                               name="search" 
                               class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white" 
                               placeholder="Search by name or index..." 
                               value="{{ request('search') }}">
                    </div>
                    <div>
                        <select name="program" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">All Programs</option>
                            @foreach($programs as $program)
                                <option value="{{ $program }}" {{ request('program') == $program ? 'selected' : '' }}>{{ $program }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <select name="department" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">All Departments</option>
                            @foreach($departments as $department)
                                <option value="{{ $department }}" {{ request('department') == $department ? 'selected' : '' }}>{{ $department }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div>
                        <select name="semester" 
                                class="w-full rounded-md border-gray-300 shadow-sm focus:border-blue-500 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <option value="">All Semesters</option>
                            @foreach($semesters as $semester)
                                <option value="{{ $semester }}" {{ request('semester') == $semester ? 'selected' : '' }}>{{ $semester }}</option>
                            @endforeach
                        </select>
                    </div>
                    <div class="flex space-x-2">
                        <button type="submit" 
                                class="w-full inline-flex justify-center items-center px-4 py-2 border border-transparent text-sm font-medium rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500">
                            <i class="fas fa-search mr-2"></i> Filter
                        </button>
                        <a href="{{ route('student-cgpa.index') }}" 
                           class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 dark:bg-gray-700 dark:border-gray-600 dark:text-white">
                            <i class="fas fa-sync"></i>
                        </a>
                    </div>
                </div>
            </form>

            <!-- Students Table -->
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                    <thead class="bg-gray-50 dark:bg-gray-700">
                        <tr>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">#</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Index Number</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Name</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">GPA</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">CGPA</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Program</th>
                            <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Semester</th>
                            <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
                        @forelse($students as $student)
                            <tr class="hover:bg-gray-50 dark:hover:bg-gray-700">
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ $loop->iteration + (($students->currentPage() - 1) * $students->perPage()) }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ $student->index_number }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                                    {{ $student->name }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    @php
                                        $gpaColor = $student->gpa < 1.5 ? 'red' : ($student->gpa < 2.0 ? 'yellow' : 'green');
                                    @endphp
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $gpaColor }}-100 text-{{ $gpaColor }}-800 dark:bg-{{ $gpaColor }}-900 dark:text-{{ $gpaColor }}-200">
                                        {{ number_format($student->gpa, 2) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm">
                                    @php
                                        $cgpaColor = $student->cgpa < 1.5 ? 'red' : ($student->cgpa < 2.0 ? 'yellow' : 'green');
                                    @endphp
                                    <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full bg-{{ $cgpaColor }}-100 text-{{ $cgpaColor }}-800 dark:bg-{{ $cgpaColor }}-900 dark:text-{{ $cgpaColor }}-200">
                                        {{ number_format($student->cgpa, 2) }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ $student->program }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-300">
                                    {{ $student->semester }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                                    <div class="flex justify-end space-x-2">
                                        <button class="view-student text-blue-600 hover:text-blue-900 dark:text-blue-400 dark:hover:text-blue-300" 
                                                data-id="{{ $student->id }}"
                                                data-tooltip="View">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                        <button class="edit-student text-indigo-600 hover:text-indigo-900 dark:text-indigo-400 dark:hover:text-indigo-300" 
                                                data-id="{{ $student->id }}"
                                                data-tooltip="Edit">
                                            <i class="fas fa-edit"></i>
                                        </button>
                                        <button class="delete-student text-red-600 hover:text-red-900 dark:text-red-400 dark:hover:text-red-300" 
                                                data-id="{{ $student->id }}"
                                                data-name="{{ $student->name }}"
                                                data-tooltip="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        @empty
                            <tr>
                                <td colspan="8" class="px-6 py-4 text-center text-sm text-gray-500 dark:text-gray-400">
                                    No students found.
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            <div class="mt-4">
                {{ $students->withQueryString()->links() }}
            </div>
                </div>
            </div>
        </div>
    </div>
</div>

@include('student-cgpa.modals.add')
@include('student-cgpa.modals.edit')
@include('student-cgpa.modals.view')
@include('student-cgpa.modals.import')
@include('student-cgpa.modals.delete')

@endsection

@push('scripts')
<script>
    document.addEventListener('DOMContentLoaded', function () {
        // Handle edit student
        document.querySelectorAll('.edit-student').forEach(button => {
            button.addEventListener('click', function () {
                const studentId = this.getAttribute('data-id');
                const url = `/student-cgpa/${studentId}/edit`;

                fetch(url, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(response => {
                    // Populate form fields
                    const form = document.getElementById('editStudentForm');
                    form.querySelector('input[name="name"]').value = response.name;
                    form.querySelector('input[name="index_number"]').value = response.index_number;
                    form.querySelector('input[name="gpa"]').value = response.gpa;
                    form.querySelector('input[name="cgpa"]').value = response.cgpa;
                    form.querySelector('input[name="totalcredit"]').value = response.totalcredit;
                    form.querySelector('input[name="totalpoint"]').value = response.totalpoint;
                    form.querySelector('input[name="program"]').value = response.program;
                    form.querySelector('input[name="department"]').value = response.department;
                    form.querySelector('input[name="semester"]').value = response.semester;
                    form.querySelector('input[name="year_of_enrollment"]').value = response.year_of_enrollment;
                    form.querySelector('input[name="email"]').value = response.email;

                    // Update form action URL
                    const updateUrl = `/student-cgpa/${studentId}`;
                    form.setAttribute('action', updateUrl);
                });

        });

        // Handle view student
        document.querySelectorAll('.view-student').forEach(button => {
            button.addEventListener('click', function() {
                const studentId = this.getAttribute('data-id');
                
                fetch(`/student-cgpa/${studentId}`, {
                    method: 'GET',
                    headers: {
                        'Accept': 'application/json',
                        'X-Requested-With': 'XMLHttpRequest',
                        'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content')
                    }
                })
                .then(response => response.json())
                .then(data => {
                    // Populate view modal
                    document.getElementById('viewStudentName').textContent = data.name;
                    document.getElementById('viewIndexNumber').textContent = data.index_number;
                    document.getElementById('viewGPA').textContent = data.gpa;
                    document.getElementById('viewCGPA').textContent = data.cgpa;
                    document.getElementById('viewTotalCredit').textContent = data.totalcredit;
                    document.getElementById('viewTotalPoint').textContent = data.totalpoint;
                    document.getElementById('viewProgram').textContent = data.program;
                    document.getElementById('viewDepartment').textContent = data.department;
                    document.getElementById('viewSemester').textContent = data.semester;
                    document.getElementById('viewYearOfEnrollment').textContent = data.year_of_enrollment;
                    document.getElementById('viewEmail').textContent = data.email || 'N/A';

                    // Show modal
                    const modal = new Modal(document.getElementById('viewStudentModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error fetching student data');
                })
                .then(() => {
                    // Show modal via Flowbite
                    const modal = new Modal(document.getElementById('editStudentModal'));
                    modal.show();
                })
                .catch(error => {
                    console.error('Error:', error);
                    alert('Error fetching student data');
                });
            });
        });
            });
        });

        // Handle delete student
        document.querySelectorAll('.delete-student').forEach(button => {
            button.addEventListener('click', function() {
                const studentId = this.getAttribute('data-id');
                const studentName = this.getAttribute('data-name');
                const deleteUrl = `/student-cgpa/${studentId}`;
                
                document.getElementById('deleteStudentName').textContent = studentName;
                document.getElementById('deleteStudentForm').setAttribute('action', deleteUrl);
                
                const modal = new Modal(document.getElementById('deleteStudentModal'));
                modal.show();
            });
        });

        // Modals are automatically initialized by Flowbite via data-modal-toggle attributes
    });
</script>
@endpush
