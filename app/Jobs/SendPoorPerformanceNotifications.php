<?php

namespace App\Jobs;

use App\Models\StudentCgpa;
use App\Notifications\PoorPerformanceNotification;
use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Notification;

class SendPoorPerformanceNotifications implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds the job can run before timing out.
     *
     * @var int
     */
    public $timeout = 120;

    /**
     * The current semester for which notifications are being sent.
     *
     * @var string
     */
    protected $semester;

    /**
     * Create a new job instance.
     *
     * @param string $semester The current semester (e.g., 'Fall 2023', 'Spring 2024')
     * @return void
     */
    public function __construct(string $semester = null)
    {
        $this->semester = $semester ?? $this->determineCurrentSemester();
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            // Get students with GPA < 1.0 or CGPA < 1.5
            $students = StudentCgpa::where('gpa', '<', 1.0)
                ->orWhere('cgpa', '<', 1.5)
                ->whereNotNull('email')
                ->where('email', '!=', '')
                ->get();

            $notifiedCount = 0;

            foreach ($students as $student) {
                try {
                    // Send notification to the student
                    $student->notify(new PoorPerformanceNotification(
                        $student->gpa,
                        $student->cgpa,
                        $this->semester
                    ));

                    $notifiedCount++;
                    
                    // Log the notification
                    Log::info('Poor performance notification sent', [
                        'student_id' => $student->id,
                        'index_number' => $student->index_number,
                        'gpa' => $student->gpa,
                        'cgpa' => $student->cgpa,
                        'semester' => $this->semester,
                    ]);
                    
                    // Small delay to prevent overwhelming the mail server
                    usleep(100000); // 100ms delay
                    
                } catch (\Exception $e) {
                    Log::error('Failed to send notification to student', [
                        'student_id' => $student->id ?? null,
                        'error' => $e->getMessage(),
                        'trace' => $e->getTraceAsString()
                    ]);
                    
                    // Continue with the next student even if one fails
                    continue;
                }
            }

            Log::info("Poor performance notifications completed", [
                'total_notified' => $notifiedCount,
                'semester' => $this->semester,
                'timestamp' => now()->toDateTimeString()
            ]);
            
        } catch (\Exception $e) {
            Log::error('Error in SendPoorPerformanceNotifications job', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            
            // Re-throw the exception to allow for job retries
            throw $e;
        }
    }
    
    /**
     * Determine the current semester based on the current date.
     *
     * @return string
     */
    protected function determineCurrentSemester(): string
    {
        $now = Carbon::now();
        $year = $now->year;
        $month = $now->month;
        
        // Spring: January - April
        if ($month >= 1 && $month <= 4) {
            return "Spring {$year}";
        }
        // Summer: May - August
        elseif ($month >= 5 && $month <= 8) {
            return "Summer {$year}";
        }
        // Fall: September - December
        else {
            return "Fall {$year}";
        }
    }
}
